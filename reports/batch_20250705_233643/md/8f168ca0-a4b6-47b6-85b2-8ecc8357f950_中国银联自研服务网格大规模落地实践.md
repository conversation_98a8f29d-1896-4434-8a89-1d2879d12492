# 中国银联自研服务网格大规模落地实践

## 會議資訊
- **研討會：** 202503 QCon Beijing
- **類型：** 主題演講
- **來源：** [https://qcon.infoq.cn/2025/beijing/presentation/6403](https://qcon.infoq.cn/2025/beijing/presentation/6403)

---

## 報告內容

這份報告根據中國銀聯在 QCon Beijing 202503 研討會上關於「自研服務網格大規模落地實踐」的主題演講內容，進行了全面而深入的分析。

---

## 中國銀聯自研服務網格大規模落地實踐：綜合分析報告

### 1. 會議概述和核心內容

本次主題演講由中國銀聯股份有限公司高級工程師李勇主講，於 2025 年 3 月的 QCon Beijing 全球軟件開發大會上舉行。演講的核心內容聚焦於中國銀聯在複雜多變的金融業務場景下，如何透過自研服務網格 (Service Mesh) 技術，成功實現大規模應用落地，並以此解決微服務治理的諸多痛點，推動金融基礎設施的數位化轉型。

演講內容分為四個主要部分：
1.  **背景與目標**：闡述了從單體應用到微服務再到服務網格的技術演進歷程，並揭示了銀聯在服務治理、業務遷移、雲上運維及服務網格原生痛點方面所面臨的挑戰，進而明確了標準化架構、統一服務治理、業務開發解耦和大規模落地支持的目標。
2.  **架構實踐**：詳細介紹了銀聯自研服務網格產品 UPMesh 的整體架構設計，包括控制面與數據面各組件的功能，以及從技術預研到全面推廣直至未來 Mesh3 的演進路線和核心思路。
3.  **應用實踐**：展示了 UPMesh 在金融級應用服務高可用、統一可觀測性方面的具體實踐，並分享了在數據面進程管理、流量治理和邊緣代理等方面的實踐經驗與痛點解決方案。
4.  **總結與展望**：回顧了大規模落地的實踐效果，特別是與傳統類庫架構的對比優勢，並展望了服務網格未來在 AI 驅動、成本優化、應用場景擴展和雲原生生態融合方面的發展趨勢。

總體而言，本次演講不僅是對銀聯技術實力的展示，更為金融行業乃至廣泛的企業級微服務治理提供了寶貴的實踐經驗和未來方向。

### 2. 技術要點和實現細節

中國銀聯自研的服務網格解決方案 UPMesh，在技術實現上展現了深度與廣度，特別是針對金融行業高可用、低延時和異構複雜性的嚴苛要求。

**2.1 總體框架與組件功能**
*   **雲上架構全景**：UPMesh 定位於銀聯雲上架構的「通用組件層」，位於基礎設施之上、平台服務與應用架構之下，承擔了服務調用、治理、監控的核心職責。這表明銀聯將服務網格視為其雲原生技術棧的核心組成部分。
*   **控制面 (Control Plane)**：
    *   **Pilot**：核心管控單元，分為「根 Pilot」和「子 Pilot」。根 Pilot 負責接收管理指令、控制規則，並與異構註冊中心交互；子 Pilot 則與 Sidecar 直接通信，下發指令。這種分層設計有助於提升大規模場景下的管控效率和穩定性。
    *   **Console**：統一的管理、配置和展示入口，簡化了運維操作。
    *   **Mixer**：負責收集 Sidecar 的監控數據，並解耦後上報給外部監控設施，實現監控數據的集中處理和轉發。
    *   **SMI (Service Mesh Interface)**：作為標準開放 API，用於對接雲管和運營平台，提升了系統的開放性和自動化能力。
*   **數據面 (Data Plane)**：
    *   **Sidecar**：作為核心的流量代理和治理執行單元，部署在應用旁。它負責服務註冊、服務發現、路由執行、協議轉換、監控數據匯報，並透過標準協議 (Mesh) 與其他 Sidecar 通信。Sidecar 的「優雅啟停」能力是保障業務連續性的關鍵。
    *   **異構註冊中心**：兼容存量服務，確保平滑遷移。

**2.2 演進路線與關鍵設計**
*   **穩步演進策略**：銀聯從 2018 年的技術預研起步，經歷了初步投產、內部試點、擴大試點、全面推廣，並規劃了 2024 年的 Mesh3 版本，這是一個典型的企業級基礎設施建設的穩健演進路徑。
*   **Mesh3 演進思路**：
    *   **「服務」、「單元」、「分組」語義清晰化**：這顯示了銀聯在實踐中對服務抽象和管理模型的不斷精煉，以適應更複雜的金融業務拓撲（如單元化部署）。
    *   **簡化用戶配置**：屏蔽了原生服務網格的複雜性，降低了用戶學習和使用的門檻。
    *   **優化控制面架構**：提升了可維護性，確保大規模部署的穩定性。
*   **異構協議治理**：
    *   **「URL 即服務」抽象**：統一了多協議（如 M2, M3）下的服務概念，簡化了治理複雜度。
    *   **多種遷移形態**：提供了「雙 SDK」、「服務網關」、「邊車直接兼容」等靈活的遷移方案，最大程度降低了存量金融業務的改造和風險成本。
    *   **平滑遷移策略**：採用「服務雙發佈 + 動態切換訂閱」的策略，實現業務的零中斷遷移，這對金融核心系統至關重要。
*   **彈性擴縮容**：透過 SMI 標準接口，結合運營平台的自動化調用，實現計劃內、告警驅動和智能算法驅動的彈性擴縮容，大幅提升了資源利用率和運維效率。

**2.3 金融級高可用與可觀測實踐**
*   **高可用性**：
    *   **節點層面**：通信隔離、單鏈路收斂複用、失效通知、進程守護、增量 xDS（最終一致性）等。
    *   **服務層面**：強弱依賴管理、失效恢復、單元優先路由、異步雙發、自動切換等。特別是針對金融場景的「Tp99 雙發」機制，確保了極端情況下的可靠性。
    *   **路由層面**：秒級生效的預置多種灰度發佈（金絲雀、藍綠、ABTest）、廣播路由、指定路由、調用方/服務方路由等，提供了強大的流量精細化控制能力。
    *   **控制面高可用**：分層分區獨立管控、分區逃生通道、邊車弱控制面依賴、灰度升級等，確保了管控自身的穩定性。
    *   **通信效率與可靠性**：採用 **IPC + UDS + 異步回調** 提升通信效率；利用 **本机 Ack + 邊車 Ack + 異常重試** 保障異步調用可靠性。
    *   **異常處理與交易一致性**：通過 **單元化部署 + 流水落庫 + 實時同步** 確保交易狀態一致性；利用 **多級心跳 + 主動通知** 及時隔離故障進程；結合 **在途交易 + 動態權重 + 半熔斷** 應對資源沖高和交易阻塞。
*   **可觀測性**：
    *   **「技術 + 業務」統一可觀測**：Mixer 解耦監控基礎設施，將應用打點數據、調用鏈數據、日誌、指標和交易流水數據整合，通過調用鏈 ID、日誌 ID 和時序指標進行關聯分析，實現了全面的技術與業務融合監控，便於快速定位問題。
    *   **豐富的監控視圖**：提供實時調用數據、服務鏈健康探測、進程內監控信息、監控拓撲圖和調用鏈跟踪詳情，為運維人員提供了全方位的洞察力。

**2.4 經驗與踩坑解決方案**
*   **數據面進程管理**：自研 **Uagent** 和 **Uproxy** 實現優雅啟停、進程守護、狀態同步，解決了微服務單容器多進程拆分、僵屍進程、不優雅停機數據丟失等痛點，提升了節點內高可用和可管理性。
*   **數據面流量治理**：簡化服務抽象和管理模型，提供多維度（系統、應用、單元、分組）的流量調配能力，並內置高級路由功能，實現靈活流量控制。
*   **邊緣代理**：實現系統間高可通信，隔離複雜性，收斂鏈路，轉換協議，有效解決了金融系統中異構服務依賴和複雜調用關係的可靠運維問題。

### 3. 商業價值和應用場景

中國銀聯自研服務網格的大規模落地，不僅是技術層面的成功，更為其核心金融業務帶來了顯著的商業價值，並開拓了廣闊的應用場景。

**3.1 核心商業價值**
*   **提升金融級服務穩定性與可用性**：作為全球支付網絡的核心，銀聯對「可用性」的要求達到了近乎苛刻的 99.999%。服務網格通過其在高可用實踐（如多級容錯、智能負載均衡、自動切換、半熔斷、精細化流量控制等）上的深度強化，有效保障了每日百萬級 QPS 的支付交易服務的連續性和穩定性，將 RTO 縮短至 20 秒以內，遷移和運行實現零中斷，直接避免了潛在的巨大業務損失和聲譽風險。
*   **顯著降低運維和開發成本**：
    *   **基礎能力下沉**：將 80% 的非業務功能（如服務治理、監控、安全等）下沉到服務網格基礎設施層，消除了重複開發，讓應用開發團隊能更專注於核心業務邏輯，大幅提升開發效率。
    *   **統一管理與自動化**：標準化服務治理平台、統一規範控制面、自動化擴縮容、全流程自動化運維，降低了複雜異構環境下的運維複雜度和人力投入。
    *   **輕量化接入**：輕薄的 SDK 使得業務系統接入更加便捷，降低了接入成本。
*   **加速業務創新與數位化轉型**：
    *   **靈活的架構演進**：支持多語言、多協議、多環境的兼容，使得銀聯能夠在不影響存量系統的情況下，逐步向雲原生架構轉型，為新技術的引入和新業務的快速迭代提供了堅實基礎。
    *   **更快的響應速度**：通過彈性擴縮容和優化的交易時延（＜50ms），能夠快速響應業務峰值和市場變化，提升了系統的適應性和競爭力。
    *   **助力綠色金融發展**：高效的資源利用率（低資源消耗的邊車、優化的集群容量）也間接支持了綠色數據中心和可持續發展目標。
*   **強化風險管控與合規性**：統一的服務治理平台和可觀測性能力，使得系統的運行狀態、調用鏈路、異常情況能夠被精準監控和分析，提升了金融業務的透明度和風險管控能力，滿足嚴格的合規要求。

**3.2 廣泛應用場景**
*   **核心支付清算系統**：作為銀聯百萬級 QPS 的全球支付網絡的基礎設施，服務網格保障了其高可用、低延時的運行。
*   **金融應用平滑遷移**：解決了金融領域大規模異構服務從傳統架構向微服務、雲原生架構平滑遷移的難題，特別是針對 M2 到 M3 協議的無感切換。
*   **多租戶/多中心部署**：服務網格的單元化、分組治理能力，使其非常適合銀聯這種多中心、多地域部署的複雜金融基礎設施。
*   **微服務化應用治理**：為銀聯內部及外部合作夥伴的微服務應用提供了統一的服務發現、負載均衡、流量管理、熔斷降級、灰度發佈等治理能力。
*   **雲原生轉型支持**：作為雲原生技術體系的重要組成部分，UPMesh 有效支持了容器化、自動化部署和彈性擴展的雲上運維模式。
*   **數據中間件強化**：通過自研數據庫中間件，實現了多分片線性擴展、多副本、主備分片自動切換，為上層應用提供更強大的數據持久化能力，這在金融數據場景中極為關鍵。

### 4. 創新亮點和技術突破

中國銀聯的自研服務網格實踐，不僅僅是應用現有技術，更是在多個維度進行了深度的創新與突破，尤其針對金融行業的特殊需求。

*   **完全自研的金融級服務網格 (UPMesh)**：在業界多數企業選擇基於 Istio/Envoy 進行二次開發的背景下，銀聯堅持自研，並圍繞核心支付場景的高可用、低延時、異構兼容等特性進行了深度定制和優化。這表明其具備強大的自主研發能力和對核心技術棧的掌控力。
*   **深度異構協議治理與平滑遷移方案**：這是其最核心的創新之一。面對銀聯龐大的、歷史悠久的多語言、多協議（M2、M3）存量系統，UPMesh 抽象出「URL 即服務」概念，並提供了「雙 SDK」、「服務網關」、「邊車直接兼容」等多種靈活的遷移形態，以及「服務雙發佈 + 動態切換訂閱」的平滑遷移策略，實現了業務零中斷的架構升級。這在處理大型、關鍵業務系統的異構性方面，提供了極具參考價值的解決方案。
*   **極致的高可用性實踐**：
    *   **針對金融場景的細緻優化**：例如「Tp99 雙發」、「在途交易 + 動態權重 + 半熔斷」、「多級心跳 + 主動通知」等機制，都體現了對金融業務極端可用性要求的深刻理解和定制化解決方案。
    *   **高效可靠的通信機制**：採用 IPC (進程間通信) + UDS (Unix Domain Socket) + 異步回調，以及本地 ACK + 邊車 ACK + 異常重試等組合，在保障通信效率的同時，極大提升了調用的可靠性。
*   **「技術+業務」融合的統一可觀測性**：通過 Mixer 解耦，將技術監控指標、調用鏈追踪、日誌和業務交易流水進行深度關聯分析，實現了從基礎設施到業務流程的全景可視化和精準故障定位。這種從業務視角出發的監控能力，遠超傳統僅限於技術層面的監控。
*   **自研數據面進程管理機制**：利用 Uagent 和 Uproxy 實現邊車和應用的優雅啟停、進程守護和內部高可用，解決了容器化微服務環境下進程管理的複雜性和穩定性問題，這是一個常被忽略但非常重要的細節。
*   **C++ 與 Rust 技術棧結合**：選擇 C++ 和 Rust 兩種語言開發邊車，並結合協程、異步 IO 技術，旨在實現極致的性能和低資源消耗（單 C 邊車 Qps > 100k, 時延 < 2 ms），這在追求性能極限的金融交易系統中是一個顯著的技術突破。
*   **「Mesh3」的下一代模型探索**：提出更清晰的「服務」、「單元」、「分組」語義，並簡化用戶配置，表明銀聯在服務網格的抽象模型上持續創新，旨在提供更符合業務場景、更易於使用的治理手段。

這些創新不僅解決了銀聯自身面臨的複雜挑戰，也為其他大型企業在數位化轉型中應用服務網格提供了範例和啟示。

### 5. 趨勢洞察和未來展望

中國銀聯的服務網格實踐不僅是當前技術能力的體現，其對未來發展趨勢的洞察也預示著行業的走向。演講中提及的「代理能力轉移，聚焦數智化轉型」是核心理念，具體體現在以下幾個方面：

**5.1 AI 驅動自動化運維 (AIOps)**
*   **趨勢洞察**：將 AI 能力融入服務網格，實現智能流量調度、預測性故障處理和自動自愈。這意味著服務網格將從被動響應轉向主動預防和優化，大幅降低人工介入，提升系統的自治能力。
*   **銀聯實踐的啟示**：銀聯已在可觀測性方面將技術與業務數據融合，為 AI 驅動的分析奠定了基礎。未來，這些數據將被用於訓練 AI 模型，實現更智能的流量分發（如基於實時負載和預測性分析）、異常行為識別和自動化故障恢復，最終實現金融系統的自我運營。

**5.2 成本優化**
*   **趨勢洞察**：
    *   **Proxyless 模式普及**：減少或消除獨立 Sidecar 代理，將部分網格能力直接內嵌到應用或運行時中，以降低額外延遲和資源消耗。
    *   **Rust 語言應用、eBPF 技術優化**：Rust 以其內存安全和高性能著稱，eBPF 則可在不修改內核代碼的情況下，高效地監控和修改網絡行為。
*   **銀聯實踐的啟示**：銀聯已在 Sidecar 開發中引入 C++ 與 Rust，並強調其低資源消耗。未來，進一步探索 Proxyless 模式和 eBPF 技術，將是銀聯在追求極致性能和成本效益上的必然選擇，尤其是在大規模部署下，任何一點資源節省都會帶來巨大的累計效益。

**5.3 應用場景擴展與創新**
*   **趨勢洞察**：
    *   **Wasm (WebAssembly) 多語言擴展**：利用 Wasm 作為 Sidecar 的擴展機制，允許開發者使用多種語言編寫自定義邏輯，並安全地運行在網格中。
    *   **數據、DB 等跨領域應用擴展**：服務網格的能力不再僅限於應用層的服務間通信，將延伸到數據訪問層，甚至實現「數據網格 (Data Mesh)」或「DB 網格 (DB Mesh)」，統一管理數據訪問和治理。
*   **銀聯實踐的啟示**：銀聯已經自研了數據庫中間件，實現多分片線性擴展，這與數據網格的趨勢不謀而合。未來，UPMesh 很可能將其治理能力從應用層擴展到數據層，為分佈式數據庫、數據集成等提供統一治理和可觀測性，進一步打破技術邊界，實現更全面的資源管理。

**5.4 深度融入雲原生生態**
*   **趨勢洞察**：
    *   **多網關功能融合**：將 API 網關、入口網關 (Ingress Gateway) 和服務網格的流量治理功能進一步融合，形成更統一的流量入口和控制平面。
    *   **多運行時協同**：與 Dapr、Knative 等雲原生運行時進一步協同，為應用提供更豐富、更統一的分佈式能力。
*   **銀聯實踐的啟示**：銀聯的雲上架構已包含 API 網關和各類雲原生組件，UPMesh 作為核心通用組件，未來將更緊密地與其他雲原生組件協同工作，形成一個更加內聚、高效的雲原生解決方案，為其全球支付網絡提供更強大的底層支撐。

**總結與展望**
中國銀聯的自研服務網格落地實踐，不僅證明了其在複雜金融領域的技術領先地位，也為整個行業展示了服務網格的巨大潛力。正如演講者所言，「服務網格，不是銀彈」，它是一個持續演進的過程。銀聯清晰地認識到，隨著數智化轉型的深入，服務網格將從單純的服務治理工具，發展成為一個由 AI 驅動、高效低成本、應用場景更廣泛、且深度融入雲原生生態的基礎設施。這種前瞻性的視野和持續創新的精神，將助力中國銀聯在未來的數位支付和金融科技競爭中保持核心優勢。

---

<div style="text-align: center; color: #666; font-size: 0.9em; margin-top: 2em;">
<em>本報告由 TrendScope 自動生成 | 生成時間：2025-07-05 23:37:27</em>
</div>
