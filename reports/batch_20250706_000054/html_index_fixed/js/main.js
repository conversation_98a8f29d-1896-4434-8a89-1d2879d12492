
        // TrendScope Hugo Theme JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化主題功能
            initializeTheme();
            initializeNavigation();
            initializeSearch();
        });

        function initializeTheme() {
            // 主題切換功能
            const themeToggle = document.querySelector('.theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', toggleTheme);
            }
        }

        function initializeNavigation() {
            // 導航功能
            const navToggle = document.querySelector('.nav-toggle');
            const navMenu = document.querySelector('.nav-menu');

            if (navToggle && navMenu) {
                navToggle.addEventListener('click', function() {
                    navMenu.classList.toggle('active');
                });
            }
        }

        function initializeSearch() {
            // 搜索功能
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('input', handleSearch);
            }
        }

        function toggleTheme() {
            document.body.classList.toggle('dark-theme');
            localStorage.setItem('theme', document.body.classList.contains('dark-theme') ? 'dark' : 'light');
        }

        function handleSearch(event) {
            const query = event.target.value.toLowerCase();
            // 實現搜索邏輯
            console.log('搜索查詢:', query);
        }
        