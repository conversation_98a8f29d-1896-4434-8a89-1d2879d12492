# 中国银联自研服务网格大规模落地实践

## 會議資訊
- **研討會：** 202503 QCon Beijing
- **類型：** 主題演講
- **來源：** [https://qcon.infoq.cn/2025/beijing/presentation/6403](https://qcon.infoq.cn/2025/beijing/presentation/6403)

---

## 報告內容

# 中國銀聯自研服務網格大規模落地實踐——綜合分析報告

## 概述

本報告旨在深入分析中國銀聯在 QCon Beijing 2025 全球軟件開發大會上題為「中國銀聯自研服務網格大規模落地實踐」的主題演講內容。該演講由中國銀聯股份有限公司高級工程師李勇主講，重點闡述了銀聯如何自主研發並大規模推廣服務網格（Service Mesh）技術，以應對其複雜的金融業務場景和異構系統環境所帶來的挑戰。報告將從會議概述、技術要點、商業價值、創新亮點及未來趨勢等多個維度，為讀者提供一份全面而深入的分析。

---

## 1. 會議概述與核心內容

### 1.1 會議概覽

*   **會議名稱**: QCon Beijing 全球軟件開發大會
*   **演講標題**: 中國銀聯自研服務網格大規模落地實踐
*   **演講者**: 李勇，中國銀聯股份有限公司 高級工程師
*   **時間**: 2025年3月（QCon Beijing）
*   **類型**: 主題演講

### 1.2 核心內容提煉

本次演講的核心是中國銀聯如何從傳統單體應用、微服務框架一路演進至服務網格架構，並成功實現其在大規模、高要求的金融生產環境中的落地。演講聚焦於解決金融級應用所面臨的服務治理困境、業務平滑遷移難題、雲上運維短板及服務網格自身配置複雜等痛點，旨在通過自研的服務網格產品 UPMe sh，實現架構標準化、服務治理統一化、業務開發解耦以及對大規模應用的支持。

核心內容涵蓋了 UPMe sh 的整體架構設計（控制面與數據面）、其演進路線與關鍵設計思路（異構協議治理、大規模應用遷移方案、彈性擴縮容），以及在實際應用中如何保障金融級高可用性、實現技術與業務統一可觀測性，並分享了寶貴的實踐經驗與「踩坑」心得（數據面進程管理、流量治理、邊緣代理）。最終，演講展示了大規模落地實踐的顯著效果，並對服務網格的未來發展趨勢進行了展望。

---

## 2. 技術要點與實現細節

中國銀聯的自研服務網格落地實踐展示了深厚的技術實力和對複雜系統的駕馭能力。

### 2.1 架構設計與組件功能

銀聯的服務網格整體架構（UPMesh）劃分為清晰的控制面和數據面：

*   **控制面 (Control Plane)**:
    *   **Console**: 統一的管理介面，負責配置、管理與展示。
    *   **SMI (Service Mesh Interface)**: 作為開放標準 API，對接雲管理與運營平台，實現標準化接口管理。
    *   **Pilot (根 Pilot & 子 Pilot)**: 核心管控組件，負責服務註冊發現、互聯互通、規則管理與指令下發。根 Pilot 與異構註冊中心交互，子 Pilot 則與 Sidecar 直接通信。
    *   **Mixer**: 專門用於收集、分析與轉送 Sidecar 上報的監控數據，並將其匯報給監控設施。
*   **數據面 (Data Plane)**:
    *   **Sidecar**: 以邊車模式部署在應用旁，是數據面的核心。負責優雅啟停、服務通信、路由執行、協議轉換，並將監控數據匯報給 Mixer。Sidecar 之間通過標準協議 Mesh 進行通信。
    *   **應用 APP**: 實際的業務應用。
    *   **異構註冊中心**: 兼容舊有服務的註冊發現機制。
    *   **監控設施**: 外部監控系統，接收 Mixer 匯報的數據。

### 2.2 異構協議治理與大規模遷移

*   **異構協議治理**: 針對金融業務中多語言、多協議、多環境的複雜現狀，銀聯升級了服務網格架構：
    *   控制面能兼容多種註冊中心，數據面能兼容多種協議。
    *   抽象「URL 即服務」概念，並通過 `協議標識://子系統名/服務名?可選參數` 的方式統一服務標識。
    *   通過 **雙 SDK 模式**、**服務網關模式**、**邊車直接兼容模式** 三種策略，因地制宜地改造存量應用，降低改造與遷移成本。
*   **大規模應用遷移方案**: 採用「服務雙發佈 + 動態切換訂閱」策略，實現平滑無感的遷移：
    *   **起始狀態**: 傳統 M2 SDK（基於 Zookeeper）的服務調用。
    *   **階段一**: 服務協議雙發佈（M2 與 M3），應用同時發佈 M2 和 Mesh SDK 服務。
    *   **階段二**: 消費者逐步切換調用 M3 服務。
    *   **最終狀態**: 所有服務完全遷移到 M3 協議，由 Mesh Pilot 統一管理。此策略顯著降低了業務遷移風險。

### 2.3 金融級高可用性實踐

金融業務對高可用性有著極致要求，銀聯在服務網格中融入了多項創新實踐：

*   **節點層面**: 管理與通信隔離、單鏈路收斂複用、失效通知、最小請求數限流、角色限流、增量 xDS（最終一致性）、進程守護等。
*   **服務層面**: 強弱依賴管理、失效恢復、單元優先調用、地域超時控制、Tp99 雙發、自動切換、服務狀態管理。
*   **路由層面**: 秒級生效的預置多種灰度發佈方式（金絲雀、藍綠、AB Test）、廣播路由、指定路由、調用方/服務方路由。
*   **控制面層面**: 分層分區獨立管控（單管控單元可達萬級節點，規則秒級生效）、分區「逃生」通道、邊車弱控制面依賴、灰度升級與邊車升級。
*   **通訊與可靠性**: 採用 **IPC + UDS + 異步回調** 提升通訊效率；**本機 Ack + 邊車 Ack + 異常重試** 保障異步調用可靠性；通過 **單元化部署 + 流水落庫 + 實時同步** 確保交易一致性；**多級心跳 + 主動通知** 快速隔離宕機進程；**在途交易 + 動態權重 + 半熔斷** 應對資源沖高、交易阻塞。
*   **網格容量與可用性**: 分層架構 + 分佈式存取 + 增量通知解決規模化容量問題；邊車緩存 + 應急「逃生」機制應對子 Pilot 失效；異步反向拉取 + 週期輪詢解決節點級狀態事件時序覆蓋問題。

### 2.4 技術與業務統一可觀測性

*   通過 Mixer 解耦監控基礎設施，對指標進行分類分級。
*   結合交易流水，實現「技術指標 + 業務指標」的融合分析與精準監控。
*   提供全面的監控儀表板，包括實時調用數據、服務鏈健康探測、進程內監控、監控拓撲圖和調用鏈跟踪詳情，為運維和業務分析提供強大支持。

### 2.5 經驗與踩坑解決方案

*   **數據面進程管理**: 通過 Uagent 和 Uproxy 的互相守護與狀態同步，解決了標準化進程守護、服務啟停、優雅升級、殭屍進程堆積、不優雅停機數據丟失等問題，保障了應用節點內的高可用與高可管理性。
*   **數據面流量治理**: 簡化服務抽象和管理模型，提供簡單易用的流量管理入口，內置高階路由（如指定 IP 調用、雙發、廣播、重試），實現對流量的靈活調配。
*   **邊緣代理 (Edge Proxy)**: 實現系統間高可靠通信，隔離複雜性，收斂鏈路，並進行協議轉換。通過附加查詢串、替換調用方集群名和替換服務 URL 等機制，解決了金融系統中異構服務依賴和複雜調用關係的可靠運維難題。

---

## 3. 商業價值與應用場景

中國銀聯自研服務網格的成功落地，不僅是技術層面的突破，更為其全球支付業務帶來了巨大的商業價值。

### 3.1 核心商業價值

*   **提升全球支付網絡穩定性與可用性**: 作為服務全球 183 個國家和地區、每秒處理百萬級 QPS 的支付網絡，99.999% 的高可用性保障了金融服務的連續性，避免了因系統故障造成的巨大經濟損失和信譽風險。RTO (恢復時間目標) 實際小於 20 秒，遠低於 2 分鐘的目標，極大提升了業務韌性。
*   **加速業務開發與創新**: 服務網格將 80% 的非業務功能下沉到專用基礎設施層，使得應用開發團隊可以更專注於核心業務邏輯，大幅提升了機構接入效率和新業務的迭代速度。輕薄 SDK 快速接入模式也降低了開發門檻。
*   **顯著降低運維成本**: 通過標準化運維接口、統一的彈性擴縮容模式、全流程自動化以及態勢感知能力，降低了大規模分佈式系統的運維複雜度和人力成本。多維度統一可觀測性也加速了故障定位與解決。
*   **平滑實現大規模異構服務遷移**: 針對金融業務服務場景複雜、大規模異構服務平滑遷移困難的痛點，服務網格提供了多種遷移形態和「雙發佈+動態切換」策略，實現了存量應用零中斷遷移和運行，保障了業務連續性。
*   **增強系統彈性與擴展性**: 實現了多單元、多中心架構下的網格化管理，支持靈活的彈性擴縮容，滿足金融業務峰值流量的處理需求，並為未來業務增長提供了堅實的基礎。
*   **助力數字化與雲原生轉型**: 服務網格作為雲原生基礎設施的核心組件，支持多語言、多協議、多環境，是銀聯構建下一代數字化雲原生技術體系的重要基石，推動了支付生態的持續發展。
*   **技術紅利長期釋放**: 經過多年持續投入和優化，UPMesh 的成功實踐將長期為銀聯提供技術競爭優勢，並促進「綠色金融」理念的實現（通過資源優化和效率提升）。

### 3.2 典型應用場景

*   **全球支付清算網絡**: 處理海量的交易請求，保障支付鏈路的高效、穩定和安全。
*   **核心金融交易系統**: 為支付、結算等關鍵業務提供高可用、低延遲的服務治理能力。
*   **異構系統集成**: 解決銀聯內部眾多歷史系統與新興技術棧的互聯互通問題，實現平滑過渡。
*   **多中心/多活架構**: 支持跨地域、跨數據中心的服務部署與容災切換，提升業務連續性。
*   **雲環境下的自動化運維**: 在雲原生環境中實現應用的快速部署、彈性擴縮容、統一監控與智能告警。
*   **新業務快速試點與上線**: 藉助服務網格的快速接入能力和灰度發佈機制，支持創新支付產品和服務的快速驗證與推廣。

---

## 4. 創新亮點與技術突破

中國銀聯的服務網格實踐在多個方面展現了獨特的創新和顯著的技術突破，尤其體現在其「自研」屬性以及對金融行業高標準的適應性上。

### 4.1 自主研發與產品化 (UPMesh)

*   **從0到1的突破**: 在服務網格技術尚處早期階段（2018年預研），銀聯就決定自主研發，而非簡單採納開源方案，顯示了其深厚的技術積澱與前瞻性。將自研產品命名為 UPMe sh，標誌著其作為核心技術產品的地位。
*   **行業特性定製**: 金融行業對安全性、穩定性、低時延、合規性有極高要求。自研使得 UPMe sh 能深度結合銀聯的業務特性和現有基礎設施，進行高度定製化開發，從而形成一套專為金融場景打造的服務治理體系，這是通用開源方案難以比擬的。

### 4.2 金融級高可用與性能優化

*   **極致的高可用保障**: 實現 99.999% 的可用性，RTO 實際小於 20 秒，這是業界領先的金融級指標。這得益於在節點、服務、路由和控制面多層次的精細化設計，如分層分區管控、邊車弱控制面依賴、多級心跳、半熔斷、Tp99 雙發等，構築了強大的韌性。
*   **通訊效率與可靠性創新**:
    *   **IPC + UDS + 異步回調**: 在數據面通訊中採用進程間通訊（IPC）、Unix Domain Socket (UDS) 和異步回調，極大地提升了 Sidecar 與應用間的通訊效率，並降低了延時。
    *   **多重 Ack + 異常重試**: 針對異步調用的可靠性問題，通過本機 Ack 與邊車 Ack 結合異常重試機制，確保消息不丟失，交易狀態一致。
*   **核心技術棧與性能**: 採用 C++ 和 Rust 等高性能語言構建，結合協程和異步 IO 技術，以及本機 IPC 通信與跨機動態負載方式，使得單個 Sidecar 能支持超過 100k QPS，且時延小於 2ms，達到業界頂尖水平。

### 4.3 靈活平滑的大規模遷移策略

*   **多形態遷移方案**: 提供了雙 SDK、服務網關、邊車直接兼容等多種靈活的遷移形態，允許不同應用根據自身情況選擇最合適的方式，降低了存量系統的改造難度和成本。
*   **零中斷平滑遷移**: 獨特的「服務雙發佈 + 動態切換訂閱」策略，使得在核心金融業務運行過程中，服務能夠在 M2 (舊協議) 和 M3 (Mesh協議) 間無縫切換，實現零中斷遷移，這是對金融業務連續性至關重要的突破。

### 4.4 統一與智能化的可觀測性

*   **技術+業務融合監控**: 將傳統的技術指標（QPS、延時、成功率）與業務交易流水深度結合，通過 Mixer 實現數據的統一收集和分發，構建了「技術+業務」一體的融合分析與精準監控體系，為快速定位問題和業務決策提供更全面的視角。
*   **全鏈路可視化**: 從監控拓撲圖、實時調用數據到詳細的調用鏈跟踪，提供了全景式的可視化能力，極大提升了故障排查效率。

### 4.5 複雜場景治理與運維自動化

*   **精細化流量治理**: 不僅支持基於權重的流量分流，還允許服務提供方和客戶端在多個維度（系統、應用、單元、分組，甚至指定 IP）進行靈活的流量調配，並內置了雙發、廣播、重試等高階路由功能。
*   **優雅進程管理**: 引入 Uagent/Uproxy 實現數據面進程的優雅啟停、異常守護和狀態同步，有效解決了微服務環境中常見的殭屍進程、數據丟失等運維痛點。
*   **邊緣代理創新應用**: 針對金融系統間複雜的異構服務依賴和多協議調用，邊緣代理模式簡化了系統間通信，隔離了複雜性，實現鏈路收斂和協議轉換，是解決大型企業級複雜互聯問題的有效方案。

---

## 5. 趨勢洞察與未來展望

中國銀聯的服務網格實踐不僅是當前技術的成功落地，也為行業的未來發展提供了寶貴的洞察。

### 5.1 服務網格的發展趨勢

演講明確指出，服務網格的未來將是「代理能力轉移，聚焦數智化轉型」。具體體現在以下幾個方面：

*   **AI 驅動自動化運維 (AIOps)**:
    *   **智能流量調度**: 藉助 AI 算法實現更智慧的流量分配和負載均衡。
    *   **預測故障處理與自愈**: 通過機器學習分析日誌和指標，預測潛在故障並自動觸發修復，減少人工干預。
    *   這是金融行業實現高可用和降低運維成本的必然方向。
*   **成本優化**:
    *   **Proxyless 模式普及**: 減少或移除 Sidecar，將部分功能直接集成到應用或通過 Kube-proxy 等方式實現，降低資源消耗和延時。
    *   **Rust 語言應用、eBPF 技術優化**: 繼續採用高性能語言（如 Rust）和 eBPF 等內核級技術，進一步提升 Sidecar 或相關組件的性能、降低資源佔用，實現更極致的成本效益。
*   **應用場景擴展與創新**:
    *   **Wasm 多語言擴展**: 藉助 WebAssembly (Wasm) 的跨語言能力，讓更多不同語言的應用能夠方便地接入服務網格，並進行更靈活的擴展。
    *   **數據、DB 等跨領域應用擴展**: 服務網格的概念不再局限於應用層面的服務調用，未來可能擴展到數據庫、消息隊列等數據層面的治理，實現更廣泛的基礎設施統一管理。
*   **深度融入雲原生生態**:
    *   **多網關功能融合**: API 網關、流量網關等邊緣組件與服務網格的深度融合，形成更統一的流量入口和治理。
    *   **多運行時協同**: 與 Dapr、Knative 等雲原生運行時組件協同工作，構建更強大的雲原生應用開發與運行平台。

### 5.2 對未來發展的啟示

*   **持續的技術演進與投入**: 服務網格並非「銀彈」，其發展是一個持續演進的過程。銀聯的 Mesh3 規劃以及對未來趨勢的洞察，表明了其對技術長期投入和不斷創新的承諾。
*   **技術與業務深度融合**: 未來服務網格的發展將更強調技術如何更好地服務於業務，特別是通過 AI 和智能化的手段，將技術能力轉化為業務價值。
*   **生態協同與開放標準**: 雖然銀聯是自研，但其對 SMI 標準的遵循，以及對雲原生生態的深度融入趨勢的關注，表明未來技術發展將更加強調開放、標準和生態協同。
*   **中國金融行業的領導力**: 銀聯的實踐證明了中國金融機構在技術創新和大規模系統建設方面的能力，為全球金融行業的數字化轉型提供了重要參考。

---

## 總結

中國銀聯自研服務網格的大規模落地實踐，是其在全球支付網絡服務中，應對複雜技術挑戰、保障業務連續性、提升運營效率的里程碑式成就。本次 QCon 演講清晰地展示了銀聯在服務網格領域的深厚技術實力、前瞻性佈局和卓越執行力。

從技術層面看，UPMesh 憑藉其精妙的架構設計、對異構環境的強大兼容、金融級別的高可用保障機制，以及在性能優化、可觀測性、流程管理等方面的諸多創新，為複雜分佈式系統的穩定運行提供了堅實的基礎。

從商業價值層面看，UPMesh 不僅顯著提升了銀聯全球支付網絡的穩定性和可用性，降低了運維成本，加速了業務創新，更為其在全球市場的競爭力注入了強大動能，成為其數字化和雲原生轉型的核心驅動力。

展望未來，銀聯對 AI 驅動運維、成本優化、場景擴展和雲原生深度融合等趨勢的洞察，預示著服務網格技術仍將持續演進，並在更廣泛的數智化轉型中發揮關鍵作用。銀聯的實踐為業界提供了寶貴的經驗，再次印證了在複雜且高要求的金融領域，自主創新和大規模落地能力是構建未來競爭優勢的關鍵所在。

---

<div style="text-align: center; color: #666; font-size: 0.9em; margin-top: 2em;">
<em>本報告由 TrendScope 自動生成 | 生成時間：2025-07-06 00:01:44</em>
</div>
