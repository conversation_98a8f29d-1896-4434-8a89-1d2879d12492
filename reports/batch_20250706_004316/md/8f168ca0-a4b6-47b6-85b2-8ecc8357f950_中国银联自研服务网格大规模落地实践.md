# 中国银联自研服务网格大规模落地实践

## 會議資訊
- **研討會：** 202503 QCon Beijing
- **類型：** 主題演講
- **來源：** [https://qcon.infoq.cn/2025/beijing/presentation/6403](https://qcon.infoq.cn/2025/beijing/presentation/6403)

---

## 報告內容

## 中國銀聯自研服務網格大規模落地實踐：綜合分析報告

### 引言

本報告旨在針對中國銀聯高級工程師李勇於 2025 年 QCon Beijing 全球軟體開發大會上題為「中國銀聯自研服務網格大規模落地實踐」的主題演講，進行全方位的綜合分析。中國銀聯作為全球領先的支付機構，其對核心技術的自研與大規模應用，特別是在服務網格（Service Mesh）領域的探索與實踐，不僅展現了其在金融科技領域的領先地位，也為業界提供了寶貴的經驗與啟示。本報告將從會議概述、技術要點、商業價值、創新亮點及趨勢展望等多維度，深入剖析此次實踐的深遠意義。

---

### 1. 會議概述與核心內容

本次主題演講聚焦於中國銀聯如何自研並大規模落地服務網格（UPMesh），以解決其在金融級微服務架構轉型過程中面臨的挑戰。演講內容結構清晰，分為四大板塊：

*   **01 背景與目標**：闡述從單體應用到微服務再到服務網格的技術演進，面臨的服務治理困境、業務遷移難題、雲上運維短板，以及服務網格自身的配置痛點，進而明確了標準化架構、統一服務治理、業務開發解耦和大規模落地支持的目標。
*   **02 架構實踐**：詳細介紹了銀聯服務網格的整體架構設計（控制面與數據面）、各組件功能（Sidecar、Console、Mixer、Pilot、SMI），並分享了從 2018 年技術預研到 2024 年 Mesh3 全面推廣的演進路線與思路。
*   **03 應用實踐**：深入探討了在高可用性、可觀測性方面的金融級實踐細節，並分享了在數據面進程管理、流量治理、邊緣代理等方面的經驗與踩坑。
*   **04 總結與展望**：總結了大規模落地的實踐效果與取得的成就，並對服務網格的未來發展趨勢進行了展望，強調其在數智化轉型中的作用。

核心內容圍繞著銀聯如何利用服務網格技術，有效應對金融業務對高可用、高性能、高可維護性的嚴苛要求，實現異構系統的統一治理與大規模平滑遷移。

---

### 2. 技術要點與實現細節

銀聯的服務網格實踐展現了其深厚的技術積累和解決複雜問題的能力。

#### 2.1 技術演進與挑戰

*   **技術演進脈絡**：清晰呈現了從單體到微服務再到服務網格的必然演進，指出服務網格的優勢在於將服務調用技術棧下沉到邊車，實現多語言維護、低業務接入成本及靈活版本升級。
*   **面臨痛點**：
    *   **服務治理困境**：金融業務對可用性與低時延要求極高，但現有服務治理能力不均、架構複雜、重複投入嚴重。
    *   **業務遷移難題**：大規模異構服務平滑遷移困難，改造與連續性風險高。
    *   **雲上運維短板**：服務管理方式不一，自動化成本高，彈性擴縮容效率低，監控分散。
    *   **服務網格痛點**：原生配置複雜冗餘，概念不夠清晰，難以適應自身演進與用戶需求。

#### 2.2 核心架構與組件

銀聯服務網格（UPMesh）採用經典的控制面與數據面分離架構：

*   **控制面 (Control Plane)**：
    *   **Console**：統一的管理、配置與展示入口。
    *   **SMI (Service Mesh Interface)**：開放標準 API，對接雲管與運營平台，實現標準化接口。
    *   **Pilot**：服務網格的大腦，負責服務註冊發現、規則管理與指令下發，並與異構註冊中心交互。分為根 Pilot 和子 Pilot，實現分層管控。
    *   **Mixer**：負責監控數據的收集、分析與轉送，將 Sidecar 的監控數據匯報給監控設施。
*   **數據面 (Data Plane)**：
    *   **Sidecar (邊車)**：核心數據面組件，部署在應用旁，負責優雅啟停、服務通信（通過標準協議 Mesh 與其他 Sidecar 通信）、路由執行、協議轉換，並匯報監控數據。
    *   **應用 APP**：實際業務邏輯的執行者，通過 Sidecar 與外部服務交互。
    *   **異構註冊中心**：兼容舊有服務的註冊發現機制。
    *   **監控設施**：外部監控系統，接收 Mixer 匯報的數據。

#### 2.3 關鍵技術實踐與細節

*   **異構協議治理**：通過抽象 `URL 即服務` 概念，並在控制面兼容註冊中心、數據面兼容多協議，實現不同協議服務的統一治理。
*   **大規模應用遷移方案**：
    *   **形態**：提供「雙 SDK」、「服務網關」和「邊車直接兼容」三種模式，因地制宜降低改造複雜度。
    *   **策略**：採用「服務雙發佈 (M2/M3) + 動態切換訂閱」的平滑遷移策略，確保業務連續性。
*   **彈性擴縮容**：通過標準單元化、服務分組模板，結合運營平台自動調用 SMI 接口，實現計劃內、告警驅動及智能算法的彈性擴縮容，提升時效性並降低運維成本。
*   **金融級高可用實踐**：
    *   **節點層面**：通信隔離、鏈路收斂複用、失效通知、增量 xDS（最終一致）、進程守護等。
    *   **服務層面**：強弱依賴、失效恢復、單元優先、地域超時、Tp99 雙發、自動切換等。
    *   **路由層面**：秒級生效、多種灰度方式（金絲雀、藍綠、ABTest）、廣播/指定/調用方/服務方路由等。
    *   **控制面層面**：分層分區管控（單管控單元支持萬級節點，秒級生效）、分區「逃生」通道、邊車弱控制面依賴、灰度升級等。
    *   **通信優化**：採用 **IPC + UDS + 異步回調** 提升通信效率；通過 **本機 Ack + 邊車 Ack + 異常重試** 保障異步調用可靠性。
    *   **異常處理**：單元化部署+流水落庫+實時同步保證交易一致性；多級心跳+主動通知及時隔離宕機進程；在途交易+動態權重+半熔斷應對資源衝高、交易阻塞。
    *   **容量與可用性權衡**：分層架構+分佈式存取+增量通知解決規模化容量；邊車緩存+應急「逃生」應對子 Pilot 失效；異步反向拉取+週期輪詢解決節點級狀態事件時序覆蓋。
*   **統一可觀測性**：Mixer 解耦監控基礎設施，結合調用鏈 ID、日誌 ID 和時序指標，實現「技術+業務」的融合分析、精准監控，並可視化展示。
*   **數據面進程管理**：自研 Uagent/Uproxy 實現進程的優雅啟停、異常快速定位、業務連續性保障，解決僵屍進程堆積和不優雅停機數據丟失問題。
*   **數據面流量治理**：提供服務方（按多維度靈活調配）和客戶端（自定義路由）的流量管理入口，支持權重分流、特殊路由（指定 IP、雙發、廣播、重試）等。
*   **邊緣代理**：實現系統間的高可通信，隔離複雜性，收斂鏈路，轉換協議，解決異構服務依賴和複雜調用關係的可靠運維問題。

---

### 3. 商業價值與應用場景

中國銀聯自研服務網格的大規模落地，其商業價值和應用場景體現在多個層面：

*   **提升金融級服務治理能力**：
    *   解決了傳統微服務架構中，服務治理能力參差不齊、架構複雜、異構服務治理困難等痛點，實現了服務治理的統一標準化。
    *   滿足了金融行業對系統可用性、低時延和穩定性的極端要求，通過高可用、高可靠的設計，保障了核心支付業務的連續穩定運行。
*   **降低開發與運維成本，提升效率**：
    *   將 80% 的非業務功能（如服務發現、負載均衡、熔斷降級、監控等）下沉到專用基礎設施層，極大減少了應用開發者的重複開發工作，使其能夠更專注於業務邏輯。
    *   標準化的服務網格平台和運維接口，降低了自動化成本，提升了彈性擴縮容的效率，使雲上運維更具競爭力。
    *   簡化了大規模異構服務的平滑遷移過程，有效降低了改造成本和業務中斷風險。
*   **支持大規模業務發展與創新**：
    *   單管控單元管理萬級節點，規則秒級生效，證明其具備支持超大規模金融交易的能力（每秒處理百萬級 QPS）。
    *   兼容多語言、多協議、多環境，為銀聯複雜的業務生態和全球支付網絡提供了統一、靈活的技術底座，助力數字化雲原生技術體系建設。
    *   通過統一可觀測性，實現「技術+業務」融合分析，為業務決策和風險控制提供更精準的數據支持。
*   **保障核心金融基礎設施安全與穩定**：
    *   在嚴苛的金融環境中，實現了服務節點數超過 20K，可用性達到 99.999% 的業界領先水平，且遷移和運行實現了零中斷。
    *   交易時延控制在 50ms 以內，邊車自身時延低於 2ms，滿足了金融交易的性能要求。
    *   自研數據庫中間件實現多分片線性擴展、多副本、主備分片自動切換，進一步保障了數據的持久化和高可用性。

這些成果直接支持了銀聯作為全球支付網絡提供商的戰略目標，為全球 183 個國家和地區的銀聯持卡人提供高效、安全的服務。

---

### 4. 創新亮點與技術突破

中國銀聯此次自研服務網格的落地實踐，不僅是技術的應用，更包含了一系列針對金融場景的創新與突破：

*   **自研核心產品 UPMesh**：擺脫對開源方案的單一依賴，實現核心技術自主可控。這一點對於金融機構而言至關重要，能更好地滿足合規性、安全性和定制化需求。
*   **Mesh3 的概念與演進**：提出更清晰簡單的「服務」、「中心單元」、「分組」語義，並在此基礎上深度兼容存量應用，大幅降低了大規模遷移的成本。這是對服務網格模型在金融場景下的一種深度抽象和優化，提升了可用性和可理解性。
*   **金融級高可用性實踐的深度細化**：
    *   **IPC + UDS + 異步回調**：採用高效的跨進程通信（IPC）和 Unix Domain Socket（UDS）結合異步回調，極致優化了邊車與應用之間的通信效率，最大限度降低時延。
    *   **多級心跳 + 主動通知** 和 **在途交易 + 動態權重 + 半熔斷**：這些機制是針對金融交易高一致性、高可靠性要求的創新，確保了異常處理的及時性和交易的健壯性。特別是「半熔斷」機制，在傳統熔斷基礎上提供了更精細的流量控制，避免全熔斷帶來的業務影響。
    *   **分層分區管控與「逃生」通道**：針對大規模部署和金融場景特點，設計了控制面的分層架構和應急「逃生」通道，在極端情況下保障系統的可用性。
*   **「技術+業務」統一可觀測性**：不僅關注底層技術指標，更通過 Mixer 解耦監控，結合交易流水，實現了從技術視角到業務視角的深度融合分析，為金融業務的精準監控與決策提供了前所未有的洞察力。
*   **數據面優雅進程管理 (Uagent/Uproxy)**：針對容器環境下微服務進程管理的痛點，自研 Uagent 和 Uproxy 實現進程的優雅啟停、故障快速定位和自動恢復，確保了數據面的高可用和業務連續性。
*   **C + Rust 技術棧與異步 IO**：在邊車的實現中採用性能卓越的 C 和 Rust 語言，結合協程和異步 IO 技術，從底層保障了邊車的穩定高效和低資源消耗，實現單邊車 QPS 超過 100K，時延低於 2ms 的頂級性能。這代表了在關鍵基礎設施領域對性能極致追求的技術選擇。
*   **邊緣代理模式**：針對金融體系中常見的異構系統間複雜調用關係，引入邊緣代理模式，簡化了系統間通信，收斂了鏈路，提升了運維可靠性。

這些創新點不僅解決了銀聯自身的實際問題，也為其他金融機構乃至廣泛的企業級雲原生轉型提供了高價值的參考範本。

---

### 5. 趨勢洞察與未來展望

演講對服務網格的未來發展趨勢進行了前瞻性展望，核心思想是「代理能力轉移，聚焦數智化轉型」，這與當前雲原生和人工智能的發展浪潮高度契合：

*   **AI 驅動自動化運維 (AIOps)**：
    *   **智能流量調度**：未來服務網格將藉助 AI，實現更智能、更精準的流量調度，例如根據實時負載、業務指標、故障預測等自動調整流量分佈，優化資源利用率和業務體驗。
    *   **預測故障處理與自愈**：AI 將被用於分析海量監控數據，預測潛在故障，並自動觸發自愈操作，從被動響應轉向主動預防，極大提升系統可用性。
*   **成本優化**：
    *   **Proxyless 模式普及**：隨著 gRPC 等協議在應用層的普及，部分代理能力將回歸到應用進程內部或以更輕量級的方式實現，減少對獨立 Sidecar 的依賴，降低資源消耗和部署複雜度。
    *   **Rust 語言應用、eBPF 技術優化**：繼續採用 Rust 等高性能、高效率語言來開發關鍵組件，並深入應用 eBPF（擴展伯克利數據包過濾器）技術，實現更高效的網絡和系統級可觀測性與流量控制，進一步降低運行成本和提升性能。
*   **應用場景擴展與創新**：
    *   **Wasm 多語言擴展**：藉助 WebAssembly (Wasm) 技術，服務網格將能夠更靈活地支持更多語言的應用，並允許用戶以標準化方式擴展網格功能。
    *   **數據、DB 等跨領域應用擴展**：服務網格的能力將不再局限於應用間的通信，有望延伸到數據庫連接、數據流治理等更廣泛的數據層面，實現更全面的治理。
*   **深度融入雲原生生態**：
    *   **多網關功能融合**：服務網格將與 API 網關、入口控制器等更緊密地結合，提供統一的流量入口和治理能力。
    *   **多運行時協同**：與 Dapr 等雲原生運行時協同，為開發者提供更豐富的基礎能力，進一步簡化應用開發。

這些趨勢表明，服務網格的未來將不僅是一個微服務通信基礎設施，更將成為連接應用、數據、基礎設施，並由 AI 賦能的智能化、自動化運營平台，在企業數智化轉型中扮演核心角色。同時，演講者強調「服務網格，不是銀彈」，這提醒業界應理性看待其價值，結合具體場景和挑戰來選擇和應用，而非盲目追隨。

---

### 總結

中國銀聯自研服務網格的大規模落地實踐，是其在金融科技領域的又一里程碑式成就。它不僅證明了服務網格技術在應對超大規模、高併發、高可靠性金融業務挑戰方面的巨大潛力，也展示了中國銀聯強大的自主研發與技術攻堅能力。

這份實踐報告清晰地描繪了從痛點分析、架構設計、演進路線到應用實踐的全過程，特別是其中針對金融級場景的諸多技術創新與優化，如異構協議治理、平滑遷移策略、金融級高可用保障、技術業務統一可觀測、以及基於 C/Rust 和 eBPF 的性能優化等，都為業界提供了寶貴的經驗。

展望未來，隨著 AI、Wasm、eBPF 等前沿技術的深度融合，服務網格將持續演進，成為企業數智化轉型不可或缺的基石。中國銀聯的此次實踐，不僅穩固了其在全球支付網絡中的核心地位，更為其他行業的雲原生轉型和數字化升級，樹立了一個極具參考價值的典範。

---

<div style="text-align: center; color: #666; font-size: 0.9em; margin-top: 2em;">
<em>本報告由 TrendScope 自動生成 | 生成時間：2025-07-06 00:43:54</em>
</div>
