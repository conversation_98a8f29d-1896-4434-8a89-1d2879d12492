# 从碎片到统一：如何用元数据湖解决多 Lakehouse 治理难题

## 會議資訊
- **研討會：** 202503 QCon Beijing
- **類型：** 主題演講
- **來源：** [https://qcon.infoq.cn/2025/beijing/presentation/6311](https://qcon.infoq.cn/2025/beijing/presentation/6311)

---

## 報告內容

## 從碎片到統一：基於元數據湖的多 Lakehouse 治理方案綜合分析報告

### 報告摘要

本報告針對 QCon Beijing 202503 主題演講「從碎片到統一：如何用元數據湖解決多 Lakehouse 治理難題」的 PPT 內容進行全面深入的分析。演講由 Datastrato 工程副總裁及 Apache Gravitino PMC 成員史少鋒先生主講，核心旨在揭示當前企業在擁抱 Lakehouse 架構過程中面臨的「多 Lakehouse 治理」挑戰，並提出以「統一元數據湖 (Unified Metadata Lake)」為核心的解決方案——Apache Gravitino。報告將從會議概述、技術要點、商業價值、創新亮點及趨勢洞察等多個維度，為讀者呈現一個全面的視角。

### 1. 會議概述和核心內容

**會議概述：**
本次演講是 2025 年 3 月 QCon 全球軟體開發大會（北京站）的一場主題分享，聚焦於當前大數據領域的熱門話題：Lakehouse 架構的演進與治理。講者史少鋒先生，作為 Apache Gravitino 的核心貢獻者，明確指出了多 Lakehouse 環境下數據碎片化、治理複雜的痛點，並將 Apache Gravitino 元數據湖作為解決方案的核心。

**核心內容提煉：**
演講內容圍繞以下五個關鍵環節展開：

1.  **Lakehouse 背景及趨勢：** 回顧數據架構從 Data Warehouse 到 Data Lake 再到 Data Lakehouse 的演進，並闡述 Lakehouse 的核心特性（ACID/MVCC、雲原生/存算分離、開放/SQL 支持、多類型/多形態）。同時指出市場上存在多種 Lakehouse 實現（Delta Lake, Apache Hudi, Iceberg, Apache Paimon），各有所長，導致企業環境中多種 Lakehouse 並存。
2.  **多 Lakehouse 治理挑戰：** 強調了多平台、多數據形態、多地域分佈等因素造成的數據孤島，以及數據治理（連接、主權、發現、分類、生命週期管理）面臨的巨大挑戰。特別指出上層應用對數據統一發現、訪問和治理的迫切需求，以及老舊數據架構平滑升級以滿足 AI 需求的難題。
3.  **統一元數據湖及核心價值：** 隆重介紹 Apache Gravitino 作為「統一的 Data/AI 目錄」，旨在提供數據統一視圖、實現元數據層面的 SSOT（Single Source of Truth）、統一訪問和治理。詳細闡述了 Gravitino 的核心架構，包括 Metalake、Catalog、Schema 等層次，以及其如何實現統一數據訪問、統一權限管控、統一命名和統一血緣追蹤。
4.  **基於統一元數據湖的 Lakehouse 架構：** 展示了 Gravitino 如何作為連接器，使各種計算引擎和應用程序能夠統一連接到不同 Lakehouse 實現。特別強調了 Gravitino 對 Iceberg REST Catalog (IRC) API 的完整實現及其增強功能，並解決了多集群和多版本 HMS（Hive Metastore）管理的複雜性。
5.  **應用案例及未來展望：** 通過 Pinterest、騰訊雲 TBDS 某大銀行項目以及小米的實際案例，印證了統一元數據湖在簡化治理、平滑升級、降本增效及支持 Data+AI 一體化方面的顯著價值。最後，分享了 Gravitino 項目的未來路線圖，展示了其持續發展的雄心。

總體而言，本次演講清晰地闡述了數據治理面臨的挑戰，並提供了一個基於開源技術的創新性解決方案，強調了元數據在未來數據和 AI 基礎設施中的核心地位。

### 2. 技術要點和實現細節

Apache Gravitino 作為核心解決方案，其技術要點和實現細節是本報告的重點。

1.  **Lakehouse 架構特性與多實現：**
    *   **核心特性：** ACID 事務、MVCC、Schema/Partition Evolution 保證數據一致性和可追溯性；雲原生和存算分離利用對象存儲彈性；開放數據格式和 SQL 支持確保互操作性；多類型/多形態數據處理能力滿足 AI/ML 需求。
    *   **多樣性與挑戰：** Delta Lake (Spark), Apache Hudi (事務型數據湖), Iceberg (開放表格式), Apache Paimon (流式數據湖) 各自的優勢和側重，導致企業內部可能同時採用多種 Lakehouse 實現，進而產生元數據不兼容、數據孤島等治理難題。

2.  **Apache Gravitino 的核心架構與設計理念：**
    *   **Metalake 概念：** Gravitino 引入 Metalake 作為最高級別的元數據組織單元，一個 Metalake 可以包含多個 Catalog。這是實現統一治理的基礎。
    *   **分層架構：**
        *   **功能層 (Functionality layer)：** 提供統一的數據處理和治理能力。
        *   **接口層 (Interface layer)：** 統一 REST APIs 和 Iceberg REST APIs，提供標準化的訪問入口。
        *   **核心 Catalog 層 (Metalake)：** 這是 Gravitino 的核心，抽象了各種數據資產的類型（如 Hive Metastore、數據倉庫、實時消息流、文件集、AI 模型等），將它們統一納管為 Catalog，每個 Catalog 下有 Schema，Schema 下有具體數據對象（Table, Fileset, Model, Topic）。
        *   **連接層 (Connection Layer)：** 負責將 Gravitino 的抽象元數據對象映射到底層的物理數據存儲。
        *   **元數據存儲 (Metadata Storage)：** 獨立存儲 Gravitino 自身的元數據，確保其可靠性和持久性。

3.  **統一數據操作的實現：**
    *   **統一數據訪問 (Unified Data Access)：**
        *   **表狀數據 (Tabular Data)：** 通過統一的 Tabular API 和連接器，實現 Spark, Trino, Flink 等引擎對 Lakehouse 中 Table 對象的 Create/Load/Alter/Drop 操作。
        *   **非表狀數據 (Non-tabular Data)：** 通過統一的 Non-tabular API 和 Gravitino Virtual FileSystem / Python FileSystem，實現 Spark, PyTorch, RAY, TensorFlow 等引擎對 Fileset 對象的統一訪問和管理，並抽象底層存儲（S3, HDFS 等）細節。
    *   **統一權限管控 (Unified Data Permission Control)：**
        *   **集中式授權：** 提供統一的 Authorization REST APIs 作為入口，管理 Privilege、SecurableObject、Role、User、Group 等權限模型。
        *   **可插拔式後端：** 授權請求通過 Metalake 分發給各 Catalog 的 Authorization Plugin，這些插件將 Gravitino 的統一授權策略轉換為特定後端（如 MySQL `GRANT` 命令、Ranger RESTful API、GCP IAM 命令）的本地權限控制命令，實現跨異構數據源的統一權限管理。
    *   **統一命名 (Unified Naming)：** 引入 `catalog.schema.asset` 的全局唯一坐標系，簡化數據資產的發現、管理和使用，降低溝通和操作成本。
    *   **統一血緣 (Unified Lineage)：** 基於統一坐標，通過多引擎、多客戶端採集，實現端到端的數據血緣追蹤，提升數據品質和可追溯性。目前正與 Openlineage 進行集成。

4.  **對 Iceberg REST Catalog 的支持與增強：**
    *   **完全兼容 Iceberg REST Catalog (IRC) API：** Gravitino 完整實現了 Iceberg REST 規範，確保與主流計算引擎的兼容性，使其能一致地訪問和管理 Iceberg 表。
    *   **互操作性與功能擴展：** Gravitino REST API 和 Iceberg REST API 可以共享相同的元數據目的地，且一個 API 的表變更可由另一個 API 加載。通過 Gravitino REST API 還能獲得如 Tag、血緣等 Iceberg REST 規範之外的擴展功能。
    *   **增強特性：**
        *   **可插拔設計：** 底層元數據存儲可切換，推薦使用 JDBC 後端。
        *   **增強的安全特性：** 支持 OAuth 認證和憑證自動分發 (Credential Vending)，支持主流雲廠商（AWS, 阿里云, Azure, GCP）。
        *   **Metrics 收集和存儲：** 提供全面的元數據使用監控統計信息。
        *   **事件監聽機制 (Event Listener)：** 允許用戶自定義對特定元數據事件的處理邏輯，實現靈活的表治理策略。

5.  **多集群和多版本管理：**
    *   解決了企業在升級 Lakehouse 架構時，多個集群存在獨立 HMS 且版本不一導致的開發和運維複雜性。
    *   Gravitino 的統一接口允許用戶和應用程序使用相同的 API 訪問不同版本的 Hive Metastore，極大簡化了升級和運維效率。

這些技術要點共同構成了 Apache Gravitino 解決多 Lakehouse 治理難題的核心能力，使其成為一個強大且開放的元數據管理平台。

### 3. 商業價值和應用場景

Apache Gravitino 統一元數據湖的方案，為企業帶來了顯著的商業價值，並適用於多種複雜的數據應用場景。

**商業價值：**

1.  **消除數據孤島，實現統一視圖：**
    *   將分佈在多個 Lakehouse 實現（Delta Lake, Hudi, Iceberg, Paimon）以及其他數據源（數據倉庫、關係型數據庫、消息隊列、文件存儲）中的元數據匯聚到一處，提供數據的統一視圖。
    *   避免數據重複拷貝和多份數據目錄維護，降低數據分散帶來的溝通成本和錯誤率。
2.  **簡化數據治理，提升合規性：**
    *   **統一權限管控：** 提供集中式的權限管理層，能夠將高層次的權限策略下推並轉換為各個數據源的本地權限控制，極大簡化了數據權限的配置和審計，確保數據安全和合規性（如滿足數據主權、隱私保護等法規要求）。
    *   **統一血緣追踪：** 端到端的血緣追蹤能力，有助於理解數據來源、轉換過程和使用方式，對於數據質量問題追溯、影響分析和合規性審計至關重要。
    *   **統一生命週期管理：** 結合血緣和使用情況，可實現數據的智能分類、推薦存儲策略和自動清理無用數據，有效降低存儲成本（如小米案例中實現 40% 降本）。
3.  **加速數據分析與 AI/ML 開發：**
    *   **數據發現與訪問提效：** 統一命名和統一訪問接口，讓數據科學家、分析師和工程師能夠更便捷地發現、理解和訪問所需數據，大幅縮短數據準備時間。
    *   **Data+AI 一體化：** 特別是針對非結構化數據（如 AI 模型、圖片、音頻）的 Fileset 抽象和統一管理，使得數據加工成果直接作為 AI 訓練的資產，打通了數據流與模型訓練流，實現 DataOps/MLOps/LLMOps 的無縫銜接，加速 AI 應用落地。
    *   **平滑升級與技術債務緩解：** 允許企業在不中斷現有業務的情況下，逐步將舊有數據架構（如 Hive Metastore）平滑升級到新的 Lakehouse 模式，降低了技術債務和轉型風險。
4.  **降低運營複雜度和成本：**
    *   統一的接口和元數據層，減少了多個引擎連接不同數據源的複雜性，降低了開發和運維難度。
    *   對多版本 Hive Metastore 的統一管理，簡化了大型企業內部複雜的多集群環境運維。
    *   通過優化數據存儲和生命週期管理，直接降低了雲存儲或自建存儲的成本。

**應用場景：**

1.  **大型企業數據治理：** 適用於擁有大量異構數據源（傳統數據倉庫、各類數據湖、消息隊列、關係型數據庫等）和多個 Lakehouse 實現的大型企業，需要解決數據孤島、統一權限、數據血緣、資產盤點等複雜治理問題。
    *   **案例：** 騰訊雲 TBDS 某大銀行項目，實現了對行內已有大數據集群和數倉集群的統一元數據管理，並落地統一權限管控機制，滿足合規需求。
2.  **AI/ML 數據平台建設：** 對於需要為機器學習、深度學習和大模型預訓練/微調提供高質量、可追溯、易於訪問的數據資產的企業。
    *   **案例：** 小米通過引入元數據湖，實現了對 AI 資產（特別是非結構化數據）的有效管理，簡化了推薦工作流，並打通了數據與 AI 的流程，顯著提升了效率。
3.  **雲原生與混合雲數據架構：** 在多雲環境或混合雲部署中，需要統一管理跨雲、跨區域的數據資產和訪問策略。Gravitino 對雲存儲的憑證管理和可插拔設計使其非常適用於此場景。
4.  **數據中台或數據湖中台建設：** 作為數據中台的核心組件，提供統一的數據服務目錄，支持上層應用快速消費數據。
5.  **數據遷移與架構升級：** 幫助企業平滑地從傳統數據倉庫或舊版數據湖架構向現代 Lakehouse 架構過渡，尤其是處理 Hive Metastore 的多版本兼容問題。
    *   **案例：** Pinterest 從多個獨立 Iceberg Catalog 轉向 Federated Iceberg Catalog，簡化了複雜環境的管理，並實現了平滑過渡。

綜上所述，Apache Gravitino 憑藉其統一的元數據管理能力，為企業解決了數據分散和治理複雜的長期痛點，不僅提升了運營效率、降低了成本，更為企業在數據智能和 AI 時代的發展奠定了堅實基礎。

### 4. 創新亮點和技術突破

Apache Gravitino 作為統一元數據湖的代表，其創新亮點和技術突破主要體現在以下幾個方面：

1.  **元數據湖 (Metadata Lake) 概念的提出與實現：**
    *   **創新：** 相較於傳統的數據目錄 (Data Catalog)，Gravitino 將元數據本身視為一類需要集中化、統一管理的「數據」，從而提出了「元數據湖」這一概念。它不僅僅是索引，更是一個活躍的、可治理的、可擴展的元數據管理平台。
    *   **突破：** 將元數據的治理能力提升到一個全新的層次，使其成為開放數據架構中的核心組件 (Next-Gen Data Catalog is the Core in New Open Data Architecture)，旨在解決多 Lakehouse 環境下元數據碎片化的根本問題。

2.  **基於 Metalake 的多層次抽象與統一管理：**
    *   **創新：** 引入 `Metalake > Catalog > Schema > Object` 的多層次元數據組織模型。這是一個高度抽象和可擴展的設計，能夠兼容並納管極其廣泛的異構數據資產類型，從傳統的關係型數據庫表到文件集、AI 模型，甚至未來新的數據形態。
    *   **突破：** 實現了對各種數據資產的「統一視圖」和「元數據層面的 SSOT (Single Source of Truth)」，這是解決數據孤島的關鍵。這種抽象能力讓上層應用無需感知底層複雜性。

3.  **統一的數據訪問與權限管控機制：**
    *   **創新：**
        *   **統一 API：** 提供一套統一的 REST API 接口來操作所有納管的數據資產的元數據，無論是表狀數據還是非表狀數據，極大簡化了開發和集成的複雜性。
        *   **可插拔授權：** 實現了集中定義和管理權限，並通過可插拔的 Authorization Plugin 將高層次策略下推並轉譯為不同數據源的原生權限控制命令。
    *   **突破：** 打破了傳統上數據安全和治理分散在各個獨立系統中的現狀，實現了真正意義上的「統一訪問和治理」，顯著提升了數據平台的安全性、合規性和管理效率。

4.  **對 Iceberg REST Catalog (IRC) API 的原生支持與功能擴展：**
    *   **創新：** 不僅完整實現了 Iceberg REST Catalog API，確保與 Iceberg 生態的深度融合和互操作性，更在其基礎上增加了增強功能（如 OAuth 認證、Credential Vending、Metrics 收集、Event Listener）。
    *   **突破：** 這使得 Gravitino 不僅是 Iceberg 的一個兼容實現，更是一個具備高級治理和監控能力的「超級」IRC 服務，為用戶提供了更豐富、更安全的選擇，同時不破壞開放標準的互操作性。

5.  **解決多版本/多集群 Hive Metastore 的痛點：**
    *   **創新：** 針對大型企業普遍存在的 Hive Metastore 多版本、多集群並存導致的運維複雜性，Gravitino 提供了統一的接口來管理這些異構的 HMS 實例。
    *   **突破：** 大幅降低了企業數據架構升級和維護的複雜度和風險，實現了平滑過渡，是大型企業落地 Lakehouse 的重要助推器。

6.  **統一命名和端到端血緣追溯：**
    *   **創新：** 引入 `catalog.schema.asset` 的全局唯一坐標，以及基於此坐標實現的多引擎、多客戶端數據採集，構建端到端統一血緣。
    *   **突破：** 這些是實現數據可觀測性 (Data Observability) 和提升數據質量、可追溯性的基石，使得數據從生產到消費的全鏈路透明可控，對於數據問題診斷、影響分析和合規審計具有極大價值。

總之，Apache Gravitino 的創新不僅僅在於單一技術點，而是在於其對整個數據治理生態的宏觀理解和微觀實現，將分散的元數據管理能力整合為一個開放、統一、可擴展的平台，為數據和 AI 時代的企業數據基礎設施提供了關鍵性的技術突破。

### 5. 趨勢洞察和未來展望

本次會議主題及 Apache Gravitino 的解決方案，深刻反映了當前大數據和 AI 領域的幾個核心趨勢，並對未來數據基礎設施的發展提供了重要的展望。

1.  **Lakehouse 架構的成熟與普及：**
    *   **趨勢洞察：** Lakehouse 已經從概念階段走向成熟，成為融合數據湖靈活性和數據倉庫管理能力的業界主流。市場上多種開源實現的並存，證明了其巨大的潛力和廣泛應用前景。企業越來越傾向於這種開放、靈活且具備事務能力的數據架構。
    *   **未來展望：** 隨著更多企業採用 Lakehouse，如何高效管理和治理日益增長的異構 Lakehouse 環境將成為核心挑戰，這也為統一元數據湖方案提供了廣闊的市場空間。Lakehouse 生態將持續豐富，互操作性將更受重視。

2.  **元數據作為核心資產的崛起：**
    *   **趨勢洞察：** 數據爆炸式增長和複雜化，使得「元數據」從一個次要概念轉變為數據基礎設施中不可或缺的、甚至是最核心的資產。對元數據的統一管理和治理，是實現數據價值、確保數據安全和合規性的先決條件。
    *   **未來展望：** 「元數據湖」的概念將會被更多地採納，成為數據平台中的「大腦」。元數據不僅僅用於描述數據，更會驅動數據處理、優化、安全和生命週期管理，成為實現「數據織網 (Data Fabric)」或「數據網格 (Data Mesh)」等更高層次數據架構的關鍵。

3.  **Data+AI 一體化加速：**
    *   **趨勢洞察：** AI，特別是大模型（LLM），對數據的需求是前所未有的，且數據類型極其多樣（結構化、非結構化、多模態數據、AI 模型本身）。傳統的數據管道和治理模式難以滿足 AI 對數據質量、實時性、可追溯性和可管理性的嚴格要求。
    *   **未來展望：** 數據基礎設施將更緊密地與 AI/ML 生態融合，形成真正的「Data+AI 一體化」平台。統一元數據湖，特別是對 Fileset 和 Model Catalog 的支持，將成為 AI 訓練、部署和治理的基石，使數據資產能更高效地為 AI 所用，並支持 LLMOps 等新的運營模式。

4.  **開放標準與生態協同：**
    *   **趨勢洞察：** 演講中多次強調開放數據格式（Iceberg, Hudi, Paimon）和開放 API（Iceberg REST, Gravitino REST），以及與 Apache 基金會的關係，這反映了業界對於開放、互操作性強的解決方案的偏好。
    *   **未來展望：** 隨著數據治理和數據計算的複雜性增加，單一供應商的封閉生態將難以滿足所有需求。開放標準和開源項目將繼續引領創新，促進不同組件之間的協同工作，形成更加 robust 和靈活的數據生態系統。

5.  **數據治理的自動化與智能化：**
    *   **趨勢洞察：** 數據治理不再僅僅是手動配置和策略執行，而是向著自動發現、智能分類、基於血緣和使用情況的自動化優化方向發展。
    *   **未來展望：** 未來的元數據湖將融入更多 AI 能力，實現元數據的自動生成、關係挖掘、異常檢測和推薦，進一步降低數據治理的人力成本，提升治理效率和精準度。

**Apache Gravitino 的未來路線圖 (Project Roadmap) 也印證了這些趨勢：**
*   **近期（v0.7 - v0.8.1）：** 專注於核心功能的完善（Fileset 管理、權限控制、獨立 Iceberg REST 服務、標籤支持）、安全強化（認證、憑證分發、審計框架）以及生態集成（Flink 連接器、Python fsspec）。
*   **中期（v0.9 - 2025 Q2）：** 進一步強化模型目錄、安全性、數據血緣支持，擴展 JDBC 數據源支持，並優化性能。這顯示其正在向一個全面的 Data/AI 目錄邁進。
*   **遠期（Future）：** 規劃支持更先進的數據湖格式（Lance, Fluss）、用戶自定義函數（UDF）、表維護、更強大的安全特性和表統計信息，預示著其將成為更為強大和智能的數據平台核心。

總結而言，本次演講不僅是對當前 Lakehouse 治理挑戰的深刻剖析，更是對未來數據基礎設施發展方向的有力預言。以 Apache Gravitino 為代表的統一元數據湖，正是順應了數據爆炸、AI 驅動、開放協作和智能治理的時代潮流，有望成為構建現代企業數據平台的關鍵基石。

---

<div style="text-align: center; color: #666; font-size: 0.9em; margin-top: 2em;">
<em>本報告由 TrendScope 自動生成 | 生成時間：2025-07-06 00:44:06</em>
</div>
